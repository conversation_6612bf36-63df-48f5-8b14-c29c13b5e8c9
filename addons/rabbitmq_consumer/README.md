# RabbitMQ Consumer for Odoo 16

A comprehensive RabbitMQ consumer implementation for Odoo 16 with Docker scaling support, connection pooling, retry mechanisms, auto-restart capabilities, and comprehensive monitoring.

## Features

- **Robust Consumer Architecture**: Auto-starting background consumers with graceful shutdown
- **Connection Management**: Connection pooling with auto-reconnection and health monitoring
- **Retry Mechanisms**: Configurable retry logic with exponential backoff and dead letter queues
- **Docker/Scaling Support**: Container-aware consumer management with duplicate prevention
- **Comprehensive Monitoring**: Health checks, performance metrics, and alerting
- **Error Handling**: Detailed error logging with traceback and recovery mechanisms
- **Configuration Management**: Flexible configuration through Odoo settings and environment variables
- **Integration Examples**: Ready-to-use examples for common integration patterns

## Installation

### Prerequisites

- Odoo 16
- RabbitMQ Server 3.8+
- Python packages: `kombu>=5.2.0`, `pika>=1.3.0`

### Step 1: Install Python Dependencies

```bash
pip install kombu>=5.2.0 pika>=1.3.0
```

### Step 2: Install the Addon

1. Copy the `rabbitmq_consumer` folder to your Odoo addons directory
2. Update the addons list: `./odoo-bin -u all -d your_database`
3. Install the addon through Odoo Apps or via command line:
   ```bash
   ./odoo-bin -i rabbitmq_consumer -d your_database
   ```

### Step 3: Configure RabbitMQ Server

Navigate to **RabbitMQ > Configuration > Servers** and create a new server configuration:

- **Name**: Your RabbitMQ Server
- **Host**: localhost (or your RabbitMQ server IP)
- **Port**: 5672
- **Username**: guest (or your RabbitMQ username)
- **Password**: guest (or your RabbitMQ password)
- **Virtual Host**: / (or your virtual host)

Test the connection to ensure it's working properly.

## Configuration

### Basic Configuration

Go to **RabbitMQ > Configuration > Settings** to configure:

#### Connection Pool Settings
- **Pool Size**: Number of connections in the pool (default: 10)
- **Max Overflow**: Maximum additional connections (default: 20)
- **Pool Timeout**: Connection timeout in seconds (default: 30)
- **Pool Recycle**: Connection recycle time in seconds (default: 3600)

#### Consumer Settings
- **Default Prefetch Count**: Messages to prefetch per consumer (default: 10)
- **Default Max Retries**: Maximum retry attempts (default: 3)
- **Default Retry Delay**: Delay between retries in seconds (default: 30)
- **Auto Start Consumers**: Enable automatic consumer startup (default: True)

#### Manager Settings
- **Heartbeat Interval**: Manager heartbeat frequency in seconds (default: 60)
- **Cleanup Interval**: Cleanup frequency in hours (default: 24)
- **Auto Start Delay**: Delay before auto-starting consumers in seconds (default: 30)

### Docker Configuration

For Docker deployments, use environment variables:

```bash
# RabbitMQ Connection
RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672
RABBITMQ_USER=odoo
RABBITMQ_PASSWORD=odoo123
RABBITMQ_VHOST=/odoo

# Container Configuration
CONTAINER_ROLE=consumer  # Options: web, consumer, worker, all
RABBITMQ_AUTO_START=1    # 1 to enable, 0 to disable
WEB_CONSUMERS_ENABLED=0  # 1 to allow web containers to run consumers

# SSL Configuration (optional)
RABBITMQ_USE_SSL=false
RABBITMQ_SSL_CERT=/path/to/cert.pem
RABBITMQ_SSL_KEY=/path/to/key.pem
RABBITMQ_SSL_CA=/path/to/ca.pem
```

### Odoo Configuration File

Add to your `odoo.conf`:

```ini
[options]
# RabbitMQ Configuration
rabbitmq_host = localhost
rabbitmq_port = 5672
rabbitmq_user = guest
rabbitmq_password = guest
rabbitmq_vhost = /
```

## Usage

### Creating a Consumer

1. Go to **RabbitMQ > Consumers > Consumers**
2. Click **Create** and fill in the details:
   - **Name**: Consumer name
   - **Server**: Select your RabbitMQ server
   - **Queue Name**: Queue to consume from
   - **Exchange Name**: Exchange to bind to (optional)
   - **Routing Key**: Routing key pattern (optional)
   - **Consumer Class**: Python class that will handle messages
   - **Consumer Method**: Method name to call for message processing
   - **Auto Start**: Enable automatic startup
   - **Max Retries**: Number of retry attempts
   - **Retry Delay**: Delay between retries

### Message Processing

Create a model method to handle messages:

```python
@api.model
def process_my_message(self, message_body, message_properties=None):
    """Process messages from RabbitMQ"""
    try:
        # Parse message
        if isinstance(message_body, str):
            data = json.loads(message_body)
        else:
            data = message_body
        
        # Process the message
        # Your business logic here
        
        return {'status': 'success'}
        
    except Exception as e:
        _logger.error(f"Error processing message: {str(e)}")
        raise
```

### Publishing Messages

```python
# Get RabbitMQ server
server = self.env['rabbitmq.server'].get_default_server()

# Publish message
server.publish_message(
    exchange='my_exchange',
    routing_key='my.routing.key',
    message=json.dumps({'key': 'value'}),
    properties={'content_type': 'application/json'}
)
```

## Docker Deployment

### Example Docker Compose

See `docker/docker-compose.example.yml` for a complete example with:
- RabbitMQ server with management UI
- PostgreSQL database
- Odoo web containers (no consumers)
- Dedicated consumer containers
- Proper networking and volumes

### Scaling Consumers

Scale consumer containers independently:

```bash
docker-compose up --scale odoo-consumer=3
```

The system automatically prevents duplicate consumers and coordinates between containers.

## Monitoring

### Dashboard

Access the monitoring dashboard at **RabbitMQ > Monitoring > Dashboard** to view:
- System health status
- Consumer statistics
- Message processing metrics
- Performance trends

### Health Checks

Health checks run automatically every 5 minutes and monitor:
- RabbitMQ server connectivity
- Consumer health and status
- Message processing performance
- Container health

### Alerts

The system generates alerts for:
- Connection failures
- Consumer failures
- High error rates
- Performance degradation

## Integration Examples

The addon includes comprehensive examples in `examples/integration_examples.py`:

### Order Processing
```python
# Setup order consumer
processor = self.env['example.order.processor']
consumer = processor.setup_order_consumer()

# Process order messages automatically
# Messages are handled by process_order_message method
```

### Inventory Synchronization
```python
# Setup inventory sync
sync = self.env['example.inventory.sync']
sync.setup_inventory_consumers()

# Publish inventory changes
sync.publish_inventory_change(product_id, location_id, old_qty, new_qty)
```

### Notification Handling
```python
# Setup notification consumer
handler = self.env['example.notification.handler']
handler.setup_notification_consumer()

# Handles email, SMS, push, and webhook notifications
```

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Check RabbitMQ server is running
   - Verify connection settings
   - Check firewall rules

2. **Consumer Not Starting**
   - Check auto-start settings
   - Verify consumer configuration
   - Check logs for errors

3. **Messages Not Processing**
   - Verify queue exists and has messages
   - Check consumer method exists
   - Review error logs

4. **High Memory Usage**
   - Reduce prefetch count
   - Implement message batching
   - Monitor connection pool size

### Logging

Enable debug logging in `odoo.conf`:

```ini
[logger_rabbitmq_consumer]
level = DEBUG
handlers = console
qualname = rabbitmq_consumer
```

### Performance Tuning

- Adjust prefetch count based on message processing time
- Use connection pooling for high-throughput scenarios
- Monitor and tune retry settings
- Scale consumer containers based on queue depth

## API Reference

### Models

- `rabbitmq.server`: RabbitMQ server configuration
- `rabbitmq.consumer`: Consumer configuration and management
- `rabbitmq.message`: Message logging and tracking
- `rabbitmq.consumer.manager`: Container and consumer coordination
- `rabbitmq.monitoring`: Health checks and monitoring

### Services

- `rabbitmq.connection.pool`: Connection pool management
- `rabbitmq.consumer.service`: Consumer service implementation
- `rabbitmq.message.processor`: Message processing logic
- `rabbitmq.startup.service`: Container initialization
- `rabbitmq.logging.service`: Enhanced logging and monitoring

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

LGPL-3 License. See LICENSE file for details.

## Support

For support and questions:
- Check the documentation
- Review the examples
- Check the monitoring dashboard
- Review logs for errors
- Create an issue in the repository
