# -*- coding: utf-8 -*-
{
    'name': 'RabbitMQ Consumer',
    'version': '********.0',
    'category': 'Technical',
    'summary': 'Robust RabbitMQ Consumer with Retry Mechanism and Auto-restart',
    'description': """
RabbitMQ Consumer for Odoo 16
=============================

This module provides a robust RabbitMQ consumer implementation using Kombu with the following features:

* **Consumer Architecture**: Background consumer service that continuously listens to RabbitMQ queues
* **Connection Management**: Connection pooling with automatic reconnection handling
* **Retry Mechanism**: Configurable retry logic with exponential backoff and dead letter queue handling
* **Auto-restart**: Automatic consumer startup on server restart using Odoo lifecycle hooks
* **Duplicate Prevention**: Prevents multiple instances of the same consumer from running
* **Error Handling**: Comprehensive error handling for connection failures and message processing
* **Configuration**: Configurable through Odoo settings (queue names, retry counts, timeouts)
* **Logging**: Proper logging for monitoring consumer health and message processing
* **Integration**: Easy integration with existing Odoo models and business logic

Requirements:
* kombu>=5.2.0
* pika>=1.3.0

Configuration:
Add RabbitMQ connection parameters to your odoo.conf:
```
rabbitmq_host = localhost
rabbitmq_port = 5672
rabbitmq_user = guest
rabbitmq_password = guest
rabbitmq_vhost = /
```
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'license': 'LGPL-3',
    'depends': [
        'base',
        'base_setup',
    ],
    'external_dependencies': {
        'python': ['kombu', 'pika'],
    },
    'data': [
        'security/ir.model.access.csv',
        'security/security.xml',
        'data/cron_jobs.xml',
        'views/rabbitmq_server_views.xml',
        'views/rabbitmq_consumer_views.xml',
        'views/rabbitmq_message_views.xml',
        'views/rabbitmq_monitoring_views.xml',
        'views/res_config_settings_views.xml',
        'views/menu_views.xml',
    ],
    'demo': [
        'demo/demo_data.xml',
    ],
    'demo': [
        'demo/demo_data.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'post_init_hook': 'post_init_hook',
    'uninstall_hook': 'uninstall_hook',
}
