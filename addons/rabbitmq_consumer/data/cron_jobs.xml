<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Consumer Manager Heartbeat Cron -->
        <record id="cron_consumer_manager_heartbeat" model="ir.cron">
            <field name="name">RabbitMQ Consumer Manager Heartbeat</field>
            <field name="model_id" ref="model_rabbitmq_consumer_manager"/>
            <field name="state">code</field>
            <field name="code">model.send_heartbeat()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="doall">False</field>
            <field name="user_id" ref="base.user_root"/>
        </record>

        <!-- Consumer Manager Cleanup Cron -->
        <record id="cron_consumer_manager_cleanup" model="ir.cron">
            <field name="name">RabbitMQ Consumer Manager Cleanup</field>
            <field name="model_id" ref="model_rabbitmq_consumer_manager"/>
            <field name="state">code</field>
            <field name="code">model.cleanup_stale_managers()</field>
            <field name="interval_number">5</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="doall">False</field>
            <field name="user_id" ref="base.user_root"/>
        </record>

        <!-- Auto-start Consumers Cron -->
        <record id="cron_auto_start_consumers" model="ir.cron">
            <field name="name">RabbitMQ Auto-start Consumers</field>
            <field name="model_id" ref="model_rabbitmq_consumer"/>
            <field name="state">code</field>
            <field name="code">
# Check if auto-start is enabled
params = env['ir.config_parameter'].sudo()
auto_start_enabled = params.get_param('rabbitmq_consumer.auto_start_consumers', 'True') == 'True'

if auto_start_enabled:
    # Only run on containers that should auto-start
    import os
    container_mode = params.get_param('rabbitmq_consumer.container_mode', 'False') == 'True'

    if not container_mode or os.environ.get('RABBITMQ_AUTO_START', '1') == '1':
        model.start_auto_consumers()
            </field>
            <field name="interval_number">2</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="doall">False</field>
            <field name="user_id" ref="base.user_root"/>
        </record>

        <!-- Message Cleanup Cron -->
        <record id="cron_message_cleanup" model="ir.cron">
            <field name="name">RabbitMQ Message Cleanup</field>
            <field name="model_id" ref="model_rabbitmq_message"/>
            <field name="state">code</field>
            <field name="code">model.cleanup_old_messages()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="doall">False</field>
            <field name="user_id" ref="base.user_root"/>
        </record>

        <!-- Health Check Cron -->
        <record id="cron_health_check" model="ir.cron">
            <field name="name">RabbitMQ Health Check</field>
            <field name="model_id" ref="model_rabbitmq_monitoring"/>
            <field name="state">code</field>
            <field name="code">model.run_health_checks()</field>
            <field name="interval_number">5</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="doall">False</field>
            <field name="user_id" ref="base.user_root"/>
        </record>

        <!-- Monitoring Cleanup Cron -->
        <record id="cron_monitoring_cleanup" model="ir.cron">
            <field name="name">RabbitMQ Monitoring Cleanup</field>
            <field name="model_id" ref="model_rabbitmq_monitoring"/>
            <field name="state">code</field>
            <field name="code">model.cleanup_old_monitoring_data()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="doall">False</field>
            <field name="user_id" ref="base.user_root"/>
        </record>

    </data>
</odoo>
