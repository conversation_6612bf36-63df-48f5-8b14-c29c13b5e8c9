<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Demo RabbitMQ Server -->
        <record id="demo_rabbitmq_server" model="rabbitmq.server">
            <field name="name">Demo RabbitMQ Server</field>
            <field name="host">localhost</field>
            <field name="port">5672</field>
            <field name="username">guest</field>
            <field name="password">guest</field>
            <field name="virtual_host">/</field>
            <field name="use_ssl">False</field>
            <field name="active">True</field>
            <field name="pool_size">5</field>
            <field name="max_overflow">10</field>
            <field name="pool_timeout">30</field>
            <field name="pool_recycle">3600</field>
        </record>
        
        <!-- Demo Consumer: Order Processing -->
        <record id="demo_consumer_orders" model="rabbitmq.consumer">
            <field name="name">Demo Order Processor</field>
            <field name="server_id" ref="demo_rabbitmq_server"/>
            <field name="queue_name">demo.orders</field>
            <field name="exchange_name">demo_exchange</field>
            <field name="routing_key">order.*</field>
            <field name="consumer_class">example.order.processor</field>
            <field name="consumer_method">process_order_message</field>
            <field name="auto_start">False</field>
            <field name="max_retries">3</field>
            <field name="retry_delay">30</field>
            <field name="prefetch_count">10</field>
            <field name="active">True</field>
            <field name="description">Demo consumer for processing order messages</field>
        </record>
        
        <!-- Demo Consumer: Inventory Updates -->
        <record id="demo_consumer_inventory" model="rabbitmq.consumer">
            <field name="name">Demo Inventory Sync</field>
            <field name="server_id" ref="demo_rabbitmq_server"/>
            <field name="queue_name">demo.inventory</field>
            <field name="exchange_name">demo_exchange</field>
            <field name="routing_key">inventory.*</field>
            <field name="consumer_class">example.inventory.sync</field>
            <field name="consumer_method">process_inventory_update</field>
            <field name="auto_start">False</field>
            <field name="max_retries">5</field>
            <field name="retry_delay">60</field>
            <field name="prefetch_count">20</field>
            <field name="active">True</field>
            <field name="description">Demo consumer for inventory synchronization</field>
        </record>
        
        <!-- Demo Consumer: Notifications -->
        <record id="demo_consumer_notifications" model="rabbitmq.consumer">
            <field name="name">Demo Notification Handler</field>
            <field name="server_id" ref="demo_rabbitmq_server"/>
            <field name="queue_name">demo.notifications</field>
            <field name="exchange_name">demo_exchange</field>
            <field name="routing_key">notification.*</field>
            <field name="consumer_class">example.notification.handler</field>
            <field name="consumer_method">process_notification</field>
            <field name="auto_start">False</field>
            <field name="max_retries">3</field>
            <field name="retry_delay">30</field>
            <field name="prefetch_count">15</field>
            <field name="active">True</field>
            <field name="description">Demo consumer for handling notifications</field>
        </record>
        
        <!-- Demo Messages for Testing -->
        <record id="demo_message_1" model="rabbitmq.message">
            <field name="queue_name">demo.orders</field>
            <field name="exchange_name">demo_exchange</field>
            <field name="routing_key">order.created</field>
            <field name="message_type">order</field>
            <field name="status">processed</field>
            <field name="message_body">{"order_id": "DEMO001", "action": "create", "customer_email": "<EMAIL>", "items": [{"product_code": "DEMO_PROD", "quantity": 2, "price": 50.0}], "total_amount": 100.0}</field>
            <field name="processing_duration">0.5</field>
            <field name="retry_count">0</field>
            <field name="container_id">demo-container</field>
        </record>
        
        <record id="demo_message_2" model="rabbitmq.message">
            <field name="queue_name">demo.inventory</field>
            <field name="exchange_name">demo_exchange</field>
            <field name="routing_key">inventory.update</field>
            <field name="message_type">inventory</field>
            <field name="status">processed</field>
            <field name="message_body">{"product_code": "DEMO_PROD", "location_code": "WH/Stock", "quantity": 100, "operation": "set", "reason": "Demo Update"}</field>
            <field name="processing_duration">0.3</field>
            <field name="retry_count">0</field>
            <field name="container_id">demo-container</field>
        </record>
        
        <record id="demo_message_3" model="rabbitmq.message">
            <field name="queue_name">demo.notifications</field>
            <field name="exchange_name">demo_exchange</field>
            <field name="routing_key">notification.email</field>
            <field name="message_type">notification</field>
            <field name="status">processed</field>
            <field name="message_body">{"type": "email", "recipient": "<EMAIL>", "subject": "Demo Notification", "message": "This is a demo notification message"}</field>
            <field name="processing_duration">1.2</field>
            <field name="retry_count">0</field>
            <field name="container_id">demo-container</field>
        </record>
        
        <!-- Demo Monitoring Records -->
        <record id="demo_monitoring_health" model="rabbitmq.monitoring">
            <field name="name">Demo System Health Check</field>
            <field name="check_type">health</field>
            <field name="status">healthy</field>
            <field name="server_id" ref="demo_rabbitmq_server"/>
            <field name="container_id">demo-container</field>
            <field name="check_result">{"timestamp": "2024-01-01T12:00:00Z", "overall_status": "healthy", "checks": {"servers": {"status": "healthy", "connected": 1}, "consumers": {"status": "healthy", "running": 3}, "messages": {"status": "healthy", "processed": 100}}}</field>
            <field name="response_time">50.5</field>
        </record>
        
        <record id="demo_monitoring_performance" model="rabbitmq.monitoring">
            <field name="name">Demo Performance Check</field>
            <field name="check_type">performance</field>
            <field name="status">healthy</field>
            <field name="container_id">demo-container</field>
            <field name="metric_value">2.5</field>
            <field name="metric_unit">error_rate_%</field>
            <field name="threshold_warning">5.0</field>
            <field name="threshold_critical">10.0</field>
            <field name="check_result">{"error_rate": 2.5, "avg_processing_time": 0.8, "total_messages": 1000, "failed_messages": 25}</field>
        </record>
        
        <!-- Demo Configuration Parameters -->
        <record id="demo_config_auto_start" model="ir.config_parameter">
            <field name="key">rabbitmq_consumer.auto_start_consumers</field>
            <field name="value">True</field>
        </record>
        
        <record id="demo_config_container_mode" model="ir.config_parameter">
            <field name="key">rabbitmq_consumer.container_mode</field>
            <field name="value">False</field>
        </record>
        
        <record id="demo_config_log_level" model="ir.config_parameter">
            <field name="key">rabbitmq_consumer.log_level</field>
            <field name="value">INFO</field>
        </record>
        
        <record id="demo_config_store_logs" model="ir.config_parameter">
            <field name="key">rabbitmq_consumer.store_logs_in_db</field>
            <field name="value">True</field>
        </record>
        
        <record id="demo_config_default_server" model="ir.config_parameter">
            <field name="key">rabbitmq_consumer.default_server_id</field>
            <field name="value" ref="demo_rabbitmq_server"/>
        </record>
        
        <!-- Demo Consumer Manager -->
        <record id="demo_consumer_manager" model="rabbitmq.consumer.manager">
            <field name="container_id">demo-container</field>
            <field name="process_id">12345</field>
            <field name="status">running</field>
            <field name="consumer_count">3</field>
            <field name="started_at" eval="datetime.datetime.now()"/>
            <field name="last_heartbeat" eval="datetime.datetime.now()"/>
        </record>
        
    </data>
</odoo>
