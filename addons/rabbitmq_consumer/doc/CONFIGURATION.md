# Configuration Guide

This guide covers all configuration options for the RabbitMQ Consumer addon.

## Configuration Hierarchy

The addon supports multiple configuration methods with the following priority (highest to lowest):

1. **Environment Variables** (Docker/Container deployments)
2. **Odoo Configuration File** (`odoo.conf`)
3. **Database Settings** (RabbitMQ > Configuration > Settings)
4. **Default Values** (Built-in defaults)

## Environment Variables

### RabbitMQ Connection

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `RABBITMQ_HOST` | RabbitMQ server hostname | localhost | rabbitmq-server |
| `RABBITMQ_PORT` | RabbitMQ server port | 5672 | 5672 |
| `RABBITMQ_USER` | RabbitMQ username | guest | odoo |
| `RABBITMQ_PASSWORD` | RabbitMQ password | guest | odoo123 |
| `RABBITMQ_VHOST` | RabbitMQ virtual host | / | /odoo |

### SSL Configuration

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `RABBITMQ_USE_SSL` | Enable SSL connection | false | true |
| `RABBITMQ_SSL_CERT` | SSL certificate path | - | /certs/client.pem |
| `RABBITMQ_SSL_KEY` | SSL private key path | - | /certs/client.key |
| `RABBITMQ_SSL_CA` | SSL CA certificate path | - | /certs/ca.pem |
| `RABBITMQ_SSL_VERIFY` | Verify SSL certificates | true | false |

### Container Configuration

| Variable | Description | Default | Options |
|----------|-------------|---------|---------|
| `CONTAINER_ROLE` | Container role | all | web, consumer, worker, all |
| `RABBITMQ_AUTO_START` | Auto-start consumers | 1 | 0, 1 |
| `WEB_CONSUMERS_ENABLED` | Allow web containers to run consumers | 0 | 0, 1 |
| `CONSUMER_STARTUP_DELAY` | Delay before starting consumers (seconds) | 30 | 0-300 |

### Connection Pool

| Variable | Description | Default | Range |
|----------|-------------|---------|-------|
| `RABBITMQ_POOL_SIZE` | Base connection pool size | 10 | 1-100 |
| `RABBITMQ_POOL_MAX_OVERFLOW` | Maximum additional connections | 20 | 0-200 |
| `RABBITMQ_POOL_TIMEOUT` | Connection timeout (seconds) | 30 | 5-300 |
| `RABBITMQ_POOL_RECYCLE` | Connection recycle time (seconds) | 3600 | 300-86400 |

### Consumer Defaults

| Variable | Description | Default | Range |
|----------|-------------|---------|-------|
| `RABBITMQ_DEFAULT_PREFETCH` | Default prefetch count | 10 | 1-1000 |
| `RABBITMQ_DEFAULT_MAX_RETRIES` | Default max retries | 3 | 0-10 |
| `RABBITMQ_DEFAULT_RETRY_DELAY` | Default retry delay (seconds) | 30 | 1-3600 |

### Logging and Monitoring

| Variable | Description | Default | Options |
|----------|-------------|---------|---------|
| `RABBITMQ_LOG_LEVEL` | Logging level | INFO | DEBUG, INFO, WARNING, ERROR |
| `RABBITMQ_LOG_FILE` | Log file path | - | /var/log/rabbitmq.log |
| `RABBITMQ_STORE_LOGS_DB` | Store logs in database | 1 | 0, 1 |
| `RABBITMQ_STORE_METRICS_DB` | Store metrics in database | 1 | 0, 1 |

## Odoo Configuration File

Add these sections to your `odoo.conf`:

```ini
[options]
# Standard Odoo configuration
addons_path = /path/to/addons
db_host = localhost
db_port = 5432
db_user = odoo
db_password = odoo

# RabbitMQ Connection
rabbitmq_host = localhost
rabbitmq_port = 5672
rabbitmq_user = guest
rabbitmq_password = guest
rabbitmq_vhost = /

# RabbitMQ SSL (optional)
rabbitmq_use_ssl = False
rabbitmq_ssl_cert = /path/to/cert.pem
rabbitmq_ssl_key = /path/to/key.pem
rabbitmq_ssl_ca = /path/to/ca.pem

# RabbitMQ Pool Configuration
rabbitmq_pool_size = 10
rabbitmq_pool_max_overflow = 20
rabbitmq_pool_timeout = 30
rabbitmq_pool_recycle = 3600

# Consumer Configuration
rabbitmq_auto_start = True
rabbitmq_default_prefetch = 10
rabbitmq_default_max_retries = 3
rabbitmq_default_retry_delay = 30

# Container Configuration
container_role = all
web_consumers_enabled = False
consumer_startup_delay = 30

# Logging
rabbitmq_log_level = INFO
rabbitmq_store_logs_db = True
rabbitmq_store_metrics_db = True

# Production Settings
workers = 4
max_cron_threads = 2
limit_memory_hard = 2684354560
limit_memory_soft = 2147483648
limit_request = 8192
limit_time_cpu = 600
limit_time_real = 1200
```

## Database Settings

Access via **RabbitMQ > Configuration > Settings**:

### Connection Pool Settings

- **Pool Size**: Number of connections to maintain in the pool
  - **Recommended**: 5-20 for most deployments
  - **High Load**: 20-50
  - **Low Load**: 2-10

- **Max Overflow**: Additional connections beyond pool size
  - **Recommended**: 1-2x pool size
  - **Formula**: `max_overflow = pool_size * 2`

- **Pool Timeout**: Time to wait for available connection
  - **Recommended**: 30 seconds
  - **High Load**: 60 seconds
  - **Low Latency**: 10 seconds

- **Pool Recycle**: Time before recycling connections
  - **Recommended**: 3600 seconds (1 hour)
  - **Long Running**: 7200 seconds (2 hours)
  - **Frequent Reconnect**: 1800 seconds (30 minutes)

### Consumer Settings

- **Default Prefetch Count**: Messages to prefetch per consumer
  - **Fast Processing**: 50-100
  - **Slow Processing**: 1-10
  - **Balanced**: 10-20

- **Default Max Retries**: Maximum retry attempts
  - **Critical Messages**: 5-10
  - **Standard Messages**: 3-5
  - **Non-Critical**: 1-3

- **Default Retry Delay**: Delay between retries
  - **Fast Retry**: 10-30 seconds
  - **Standard**: 30-60 seconds
  - **Slow Retry**: 60-300 seconds

- **Auto Start Consumers**: Enable automatic consumer startup
  - **Production**: Enabled
  - **Development**: Disabled
  - **Testing**: Disabled

### Manager Settings

- **Heartbeat Interval**: Manager heartbeat frequency
  - **Recommended**: 60 seconds
  - **High Availability**: 30 seconds
  - **Low Frequency**: 120 seconds

- **Cleanup Interval**: Cleanup frequency for old records
  - **Recommended**: 24 hours
  - **High Volume**: 12 hours
  - **Low Volume**: 48 hours

- **Auto Start Delay**: Delay before auto-starting consumers
  - **Fast Start**: 10-30 seconds
  - **Standard**: 30-60 seconds
  - **Safe Start**: 60-120 seconds

### Docker/Container Settings

- **Container Mode**: Enable container-aware features
  - **Docker Deployment**: Enabled
  - **Standard Deployment**: Disabled

- **Container Role**: Define container responsibilities
  - **web**: Web interface only, no consumers
  - **consumer**: Dedicated consumer containers
  - **worker**: Background processing
  - **all**: All functions (development)

- **Web Consumers Enabled**: Allow web containers to run consumers
  - **Production**: Disabled (use dedicated consumer containers)
  - **Development**: Enabled
  - **Small Deployment**: Enabled

## Performance Tuning

### High Throughput Configuration

```ini
# High throughput settings
rabbitmq_pool_size = 50
rabbitmq_pool_max_overflow = 100
rabbitmq_default_prefetch = 100
workers = 8
max_cron_threads = 4
```

### Low Latency Configuration

```ini
# Low latency settings
rabbitmq_pool_timeout = 10
rabbitmq_default_prefetch = 1
rabbitmq_default_retry_delay = 10
```

### Memory Optimized Configuration

```ini
# Memory optimized settings
rabbitmq_pool_size = 5
rabbitmq_pool_max_overflow = 10
rabbitmq_default_prefetch = 5
rabbitmq_pool_recycle = 1800
```

### High Availability Configuration

```ini
# High availability settings
rabbitmq_default_max_retries = 10
rabbitmq_default_retry_delay = 60
heartbeat_interval = 30
cleanup_interval = 12
```

## Security Configuration

### SSL/TLS Setup

1. **Generate Certificates**
   ```bash
   # Create CA
   openssl genrsa -out ca.key 4096
   openssl req -new -x509 -days 365 -key ca.key -out ca.crt
   
   # Create server certificate
   openssl genrsa -out server.key 4096
   openssl req -new -key server.key -out server.csr
   openssl x509 -req -days 365 -in server.csr -CA ca.crt -CAkey ca.key -out server.crt
   
   # Create client certificate
   openssl genrsa -out client.key 4096
   openssl req -new -key client.key -out client.csr
   openssl x509 -req -days 365 -in client.csr -CA ca.crt -CAkey ca.key -out client.crt
   ```

2. **Configure RabbitMQ Server**
   ```erlang
   % /etc/rabbitmq/rabbitmq.conf
   listeners.ssl.default = 5671
   ssl_options.cacertfile = /path/to/ca.crt
   ssl_options.certfile = /path/to/server.crt
   ssl_options.keyfile = /path/to/server.key
   ssl_options.verify = verify_peer
   ssl_options.fail_if_no_peer_cert = true
   ```

3. **Configure Odoo**
   ```ini
   rabbitmq_use_ssl = True
   rabbitmq_port = 5671
   rabbitmq_ssl_cert = /path/to/client.crt
   rabbitmq_ssl_key = /path/to/client.key
   rabbitmq_ssl_ca = /path/to/ca.crt
   ```

### Authentication and Authorization

1. **Create Dedicated User**
   ```bash
   sudo rabbitmqctl add_user odoo_consumer secure_password
   sudo rabbitmqctl set_user_tags odoo_consumer monitoring
   sudo rabbitmqctl set_permissions -p /odoo odoo_consumer "^odoo\\..*" "^odoo\\..*" "^odoo\\..*"
   ```

2. **Limit Permissions**
   ```bash
   # Read-only access to specific queues
   sudo rabbitmqctl set_permissions -p /odoo readonly_user "" "^readonly\\..*" "^readonly\\..*"
   
   # Write-only access for publishers
   sudo rabbitmqctl set_permissions -p /odoo publisher_user "^publish\\..*" "^publish\\..*" ""
   ```

## Monitoring Configuration

### Health Check Settings

- **Check Interval**: 5 minutes (production), 1 minute (development)
- **Timeout**: 30 seconds
- **Retry Count**: 3
- **Alert Thresholds**:
  - Warning: 5% error rate
  - Critical: 10% error rate

### Metrics Collection

- **Performance Metrics**: Enabled
- **Connection Metrics**: Enabled
- **Consumer Metrics**: Enabled
- **Message Metrics**: Enabled
- **Retention Period**: 30 days

### Alerting

Configure alerts for:
- Connection failures
- Consumer failures
- High error rates
- Performance degradation
- Queue depth thresholds

## Troubleshooting Configuration

### Common Configuration Issues

1. **Connection Refused**
   - Check `rabbitmq_host` and `rabbitmq_port`
   - Verify RabbitMQ server is running
   - Check firewall rules

2. **Authentication Failed**
   - Verify `rabbitmq_user` and `rabbitmq_password`
   - Check user permissions in RabbitMQ

3. **SSL Errors**
   - Verify certificate paths
   - Check certificate validity
   - Ensure proper permissions on certificate files

4. **Performance Issues**
   - Adjust pool size and prefetch count
   - Monitor connection usage
   - Check message processing times

### Configuration Validation

Use the built-in validation tools:

1. **Test Connection**: RabbitMQ > Configuration > Servers > Test Connection
2. **Health Check**: RabbitMQ > Monitoring > Dashboard
3. **Consumer Status**: RabbitMQ > Consumers > Consumers
4. **System Logs**: Check Odoo logs for configuration errors

## Best Practices

1. **Use Environment Variables** for sensitive data (passwords, certificates)
2. **Separate Roles** in production (web vs consumer containers)
3. **Monitor Performance** and adjust settings based on metrics
4. **Test Configuration** in staging before production deployment
5. **Document Changes** and maintain configuration version control
6. **Regular Backups** of configuration and certificates
7. **Security Reviews** of permissions and access controls

## Configuration Templates

### Development Environment
```bash
# .env for development
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_VHOST=/
CONTAINER_ROLE=all
RABBITMQ_AUTO_START=1
WEB_CONSUMERS_ENABLED=1
RABBITMQ_LOG_LEVEL=DEBUG
```

### Production Environment
```bash
# .env for production
RABBITMQ_HOST=rabbitmq-cluster.internal
RABBITMQ_PORT=5671
RABBITMQ_USER=odoo_prod
RABBITMQ_PASSWORD=${RABBITMQ_PASSWORD}
RABBITMQ_VHOST=/production
RABBITMQ_USE_SSL=true
CONTAINER_ROLE=consumer
RABBITMQ_AUTO_START=1
WEB_CONSUMERS_ENABLED=0
RABBITMQ_LOG_LEVEL=INFO
RABBITMQ_POOL_SIZE=20
RABBITMQ_POOL_MAX_OVERFLOW=40
```
