# Installation Guide

This guide provides detailed instructions for installing and configuring the RabbitMQ Consumer addon for Odoo 16.

## Prerequisites

### System Requirements

- **Odoo 16**: The addon is specifically designed for Odoo 16
- **Python 3.8+**: Required for Odoo 16
- **RabbitMQ Server 3.8+**: Message broker
- **PostgreSQL 12+**: Database (standard Odoo requirement)

### Python Dependencies

The addon requires the following Python packages:

```bash
pip install kombu>=5.2.0 pika>=1.3.0
```

**Note**: These dependencies are automatically checked during addon installation.

## Installation Methods

### Method 1: Standard Odoo Installation

1. **Download the Addon**
   ```bash
   cd /path/to/odoo/addons
   git clone <repository-url> rabbitmq_consumer
   # or copy the addon folder manually
   ```

2. **Install Python Dependencies**
   ```bash
   pip install -r rabbitmq_consumer/requirements.txt
   ```

3. **Update Addons List**
   ```bash
   ./odoo-bin -u all -d your_database --addons-path=/path/to/addons
   ```

4. **Install the Addon**
   - Via Odoo Interface: Go to Apps → Search "RabbitMQ Consumer" → Install
   - Via Command Line:
     ```bash
     ./odoo-bin -i rabbitmq_consumer -d your_database
     ```

### Method 2: Docker Installation

1. **Create Dockerfile**
   ```dockerfile
   FROM odoo:16.0
   
   USER root
   
   # Install Python dependencies
   RUN pip install kombu>=5.2.0 pika>=1.3.0
   
   # Copy addon
   COPY ./addons/rabbitmq_consumer /mnt/extra-addons/rabbitmq_consumer
   
   USER odoo
   ```

2. **Build and Run**
   ```bash
   docker build -t odoo-rabbitmq .
   docker run -d --name odoo-rabbitmq \
     -p 8069:8069 \
     -e HOST=postgres \
     -e USER=odoo \
     -e PASSWORD=odoo \
     odoo-rabbitmq
   ```

### Method 3: Docker Compose (Recommended for Production)

Use the provided `docker/docker-compose.example.yml`:

```bash
cd rabbitmq_consumer/docker
cp docker-compose.example.yml docker-compose.yml
cp .env.example .env

# Edit .env file with your configuration
nano .env

# Start services
docker-compose up -d
```

## RabbitMQ Server Setup

### Local Installation

#### Ubuntu/Debian
```bash
# Install RabbitMQ
sudo apt-get update
sudo apt-get install rabbitmq-server

# Enable management plugin
sudo rabbitmq-plugins enable rabbitmq_management

# Create user for Odoo
sudo rabbitmqctl add_user odoo odoo123
sudo rabbitmqctl set_user_tags odoo administrator
sudo rabbitmqctl set_permissions -p / odoo ".*" ".*" ".*"

# Create virtual host (optional)
sudo rabbitmqctl add_vhost /odoo
sudo rabbitmqctl set_permissions -p /odoo odoo ".*" ".*" ".*"
```

#### CentOS/RHEL
```bash
# Install RabbitMQ
sudo yum install epel-release
sudo yum install rabbitmq-server

# Start and enable service
sudo systemctl start rabbitmq-server
sudo systemctl enable rabbitmq-server

# Enable management plugin
sudo rabbitmq-plugins enable rabbitmq_management

# Create user
sudo rabbitmqctl add_user odoo odoo123
sudo rabbitmqctl set_user_tags odoo administrator
sudo rabbitmqctl set_permissions -p / odoo ".*" ".*" ".*"
```

### Docker RabbitMQ

```bash
docker run -d --name rabbitmq \
  -p 5672:5672 \
  -p 15672:15672 \
  -e RABBITMQ_DEFAULT_USER=odoo \
  -e RABBITMQ_DEFAULT_PASS=odoo123 \
  -e RABBITMQ_DEFAULT_VHOST=/odoo \
  rabbitmq:3.12-management
```

### Cloud RabbitMQ (CloudAMQP, Amazon MQ, etc.)

For cloud-hosted RabbitMQ services, obtain the connection details and use them in the Odoo configuration.

## Post-Installation Configuration

### 1. Configure RabbitMQ Server in Odoo

1. Navigate to **RabbitMQ > Configuration > Servers**
2. Click **Create** and enter:
   - **Name**: Production RabbitMQ
   - **Host**: localhost (or your server IP)
   - **Port**: 5672
   - **Username**: odoo
   - **Password**: odoo123
   - **Virtual Host**: /odoo
   - **Use SSL**: Enable if using SSL
3. Click **Test Connection** to verify
4. Save the configuration

### 2. Configure System Settings

1. Navigate to **RabbitMQ > Configuration > Settings**
2. Configure the following sections:

#### Connection Pool Settings
- **Pool Size**: 10 (adjust based on load)
- **Max Overflow**: 20
- **Pool Timeout**: 30 seconds
- **Pool Recycle**: 3600 seconds

#### Consumer Settings
- **Default Prefetch Count**: 10
- **Default Max Retries**: 3
- **Default Retry Delay**: 30 seconds
- **Auto Start Consumers**: Enabled

#### Manager Settings
- **Heartbeat Interval**: 60 seconds
- **Cleanup Interval**: 24 hours
- **Auto Start Delay**: 30 seconds

### 3. Environment Variables (Docker/Production)

Set the following environment variables:

```bash
# RabbitMQ Connection
export RABBITMQ_HOST=rabbitmq-server
export RABBITMQ_PORT=5672
export RABBITMQ_USER=odoo
export RABBITMQ_PASSWORD=odoo123
export RABBITMQ_VHOST=/odoo

# Container Configuration
export CONTAINER_ROLE=consumer
export RABBITMQ_AUTO_START=1
export WEB_CONSUMERS_ENABLED=0

# SSL Configuration (if needed)
export RABBITMQ_USE_SSL=true
export RABBITMQ_SSL_CERT=/path/to/cert.pem
export RABBITMQ_SSL_KEY=/path/to/key.pem
export RABBITMQ_SSL_CA=/path/to/ca.pem
```

### 4. Odoo Configuration File

Add to your `odoo.conf`:

```ini
[options]
# Standard Odoo configuration
addons_path = /path/to/addons,/path/to/rabbitmq_consumer

# RabbitMQ Configuration
rabbitmq_host = localhost
rabbitmq_port = 5672
rabbitmq_user = odoo
rabbitmq_password = odoo123
rabbitmq_vhost = /odoo

# Logging
log_level = info
logfile = /var/log/odoo/odoo.log

# Workers (for production)
workers = 4
max_cron_threads = 2
```

## Verification

### 1. Check Installation

1. Navigate to **RabbitMQ** menu in Odoo
2. Verify all menu items are accessible:
   - Consumers
   - Messages
   - Monitoring
   - Configuration

### 2. Test Connection

1. Go to **RabbitMQ > Configuration > Servers**
2. Select your server and click **Test Connection**
3. Verify "Connection successful" message

### 3. Check Services

1. Go to **RabbitMQ > Monitoring > Dashboard**
2. Verify system status shows as "Healthy"
3. Check that monitoring data is being collected

### 4. Test Consumer Creation

1. Go to **RabbitMQ > Consumers > Consumers**
2. Create a test consumer:
   - **Name**: Test Consumer
   - **Queue Name**: test.queue
   - **Consumer Class**: (leave empty for testing)
   - **Auto Start**: Disabled
3. Save and verify no errors occur

## Troubleshooting

### Common Installation Issues

#### 1. Python Dependencies Missing
```bash
# Error: ModuleNotFoundError: No module named 'kombu'
pip install kombu>=5.2.0 pika>=1.3.0
```

#### 2. RabbitMQ Connection Failed
- Check RabbitMQ server is running: `sudo systemctl status rabbitmq-server`
- Verify firewall allows port 5672
- Check credentials and virtual host
- Review RabbitMQ logs: `/var/log/rabbitmq/`

#### 3. Permission Errors
```bash
# Grant proper permissions
sudo rabbitmqctl set_permissions -p /odoo odoo ".*" ".*" ".*"
```

#### 4. Addon Not Loading
- Check addon is in addons path
- Verify `__manifest__.py` is valid
- Check Odoo logs for import errors
- Ensure all dependencies are installed

### Performance Issues

#### 1. High Memory Usage
- Reduce connection pool size
- Lower prefetch count
- Implement message batching

#### 2. Slow Message Processing
- Increase prefetch count
- Scale consumer containers
- Optimize message processing logic

#### 3. Connection Timeouts
- Increase pool timeout
- Check network latency
- Verify RabbitMQ server resources

### Logging and Debugging

#### Enable Debug Logging
Add to `odoo.conf`:
```ini
[logger_rabbitmq_consumer]
level = DEBUG
handlers = console
qualname = rabbitmq_consumer
```

#### Check Logs
- Odoo logs: `/var/log/odoo/odoo.log`
- RabbitMQ logs: `/var/log/rabbitmq/`
- Container logs: `docker logs container_name`

## Next Steps

After successful installation:

1. **Create Your First Consumer**: Follow the usage guide to create consumers for your specific use cases
2. **Set Up Monitoring**: Configure alerts and monitoring dashboards
3. **Implement Integration**: Use the provided examples to integrate with your business logic
4. **Scale for Production**: Configure multiple consumer containers for high availability

## Support

For additional support:
- Check the main README.md for usage examples
- Review the monitoring dashboard for system health
- Check the integration examples in the `examples/` directory
- Review logs for detailed error information
