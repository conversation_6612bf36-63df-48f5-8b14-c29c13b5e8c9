# -*- coding: utf-8 -*-
"""
RabbitMQ Consumer Integration Examples

This file contains examples of how to integrate RabbitMQ consumers
with existing Odoo models and business logic.
"""

import json
import logging
from datetime import datetime

from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class ExampleOrderProcessor(models.Model):
    """
    Example: Processing order notifications from RabbitMQ
    
    This example shows how to create a consumer that processes
    order-related messages and updates Odoo records accordingly.
    """
    _name = 'example.order.processor'
    _description = 'Example Order Processor'

    @api.model
    def setup_order_consumer(self):
        """Setup a consumer for order processing"""
        try:
            # Get or create RabbitMQ server
            server = self.env['rabbitmq.server'].get_default_server()
            
            # Create consumer configuration
            consumer_vals = {
                'name': 'Order Processor',
                'server_id': server.id,
                'queue_name': 'orders.processing',
                'exchange_name': 'orders',
                'routing_key': 'order.*',
                'consumer_class': 'example.order.processor',
                'consumer_method': 'process_order_message',
                'auto_start': True,
                'max_retries': 3,
                'retry_delay': 30,
                'prefetch_count': 10,
                'active': True,
            }
            
            # Check if consumer already exists
            existing_consumer = self.env['rabbitmq.consumer'].search([
                ('name', '=', consumer_vals['name'])
            ])
            
            if existing_consumer:
                existing_consumer.write(consumer_vals)
                consumer = existing_consumer
            else:
                consumer = self.env['rabbitmq.consumer'].create(consumer_vals)
            
            _logger.info(f"Order consumer setup completed: {consumer.name}")
            return consumer
            
        except Exception as e:
            _logger.error(f"Error setting up order consumer: {str(e)}")
            raise UserError(_("Failed to setup order consumer: %s") % str(e))

    @api.model
    def process_order_message(self, message_body, message_properties=None):
        """
        Process order messages from RabbitMQ
        
        Expected message format:
        {
            "order_id": "ORD001",
            "action": "create|update|cancel",
            "customer_email": "<EMAIL>",
            "items": [
                {"product_code": "PROD001", "quantity": 2, "price": 100.0}
            ],
            "total_amount": 200.0,
            "timestamp": "2024-01-01T12:00:00Z"
        }
        """
        try:
            # Parse message
            if isinstance(message_body, str):
                order_data = json.loads(message_body)
            else:
                order_data = message_body
            
            # Validate required fields
            required_fields = ['order_id', 'action', 'customer_email']
            for field in required_fields:
                if field not in order_data:
                    raise ValidationError(_("Missing required field: %s") % field)
            
            # Process based on action
            action = order_data['action']
            
            if action == 'create':
                return self._create_order(order_data)
            elif action == 'update':
                return self._update_order(order_data)
            elif action == 'cancel':
                return self._cancel_order(order_data)
            else:
                raise ValidationError(_("Unknown action: %s") % action)
                
        except Exception as e:
            _logger.error(f"Error processing order message: {str(e)}")
            # Log to monitoring system
            self.env['rabbitmq.logging.service'].log_error_with_traceback(
                e, {'message_body': message_body, 'message_properties': message_properties}
            )
            raise

    def _create_order(self, order_data):
        """Create a new order from message data"""
        try:
            # Find or create customer
            customer = self._get_or_create_customer(order_data['customer_email'])
            
            # Create sale order (example using sale.order model)
            order_vals = {
                'name': order_data['order_id'],
                'partner_id': customer.id,
                'date_order': datetime.now(),
                'state': 'draft',
                'origin': 'RabbitMQ Import',
            }
            
            # Add order lines if items are provided
            if 'items' in order_data:
                order_lines = []
                for item in order_data['items']:
                    product = self._get_product_by_code(item['product_code'])
                    if product:
                        line_vals = {
                            'product_id': product.id,
                            'product_uom_qty': item['quantity'],
                            'price_unit': item['price'],
                        }
                        order_lines.append((0, 0, line_vals))
                
                order_vals['order_line'] = order_lines
            
            # Create the order
            order = self.env['sale.order'].create(order_vals)
            
            _logger.info(f"Created order {order.name} from RabbitMQ message")
            
            # Log success
            self.env['rabbitmq.logging.service'].log_performance_metric(
                'order_created', 1, 'count',
                {'order_id': order.name, 'customer': customer.name}
            )
            
            return {'status': 'success', 'order_id': order.id, 'order_name': order.name}
            
        except Exception as e:
            _logger.error(f"Error creating order: {str(e)}")
            raise

    def _update_order(self, order_data):
        """Update an existing order from message data"""
        try:
            # Find existing order
            order = self.env['sale.order'].search([
                ('name', '=', order_data['order_id'])
            ], limit=1)
            
            if not order:
                raise ValidationError(_("Order not found: %s") % order_data['order_id'])
            
            # Update order fields
            update_vals = {}
            
            if 'total_amount' in order_data:
                # You might want to validate the total amount
                pass
            
            if 'items' in order_data:
                # Update order lines (simplified example)
                order.order_line.unlink()  # Remove existing lines
                
                order_lines = []
                for item in order_data['items']:
                    product = self._get_product_by_code(item['product_code'])
                    if product:
                        line_vals = {
                            'product_id': product.id,
                            'product_uom_qty': item['quantity'],
                            'price_unit': item['price'],
                        }
                        order_lines.append((0, 0, line_vals))
                
                update_vals['order_line'] = order_lines
            
            if update_vals:
                order.write(update_vals)
            
            _logger.info(f"Updated order {order.name} from RabbitMQ message")
            
            return {'status': 'success', 'order_id': order.id, 'order_name': order.name}
            
        except Exception as e:
            _logger.error(f"Error updating order: {str(e)}")
            raise

    def _cancel_order(self, order_data):
        """Cancel an order from message data"""
        try:
            # Find existing order
            order = self.env['sale.order'].search([
                ('name', '=', order_data['order_id'])
            ], limit=1)
            
            if not order:
                raise ValidationError(_("Order not found: %s") % order_data['order_id'])
            
            # Cancel the order
            if order.state in ['draft', 'sent']:
                order.action_cancel()
            else:
                _logger.warning(f"Cannot cancel order {order.name} in state {order.state}")
                return {'status': 'warning', 'message': 'Order cannot be cancelled in current state'}
            
            _logger.info(f"Cancelled order {order.name} from RabbitMQ message")
            
            return {'status': 'success', 'order_id': order.id, 'order_name': order.name}
            
        except Exception as e:
            _logger.error(f"Error cancelling order: {str(e)}")
            raise

    def _get_or_create_customer(self, email):
        """Get or create customer by email"""
        customer = self.env['res.partner'].search([
            ('email', '=', email),
            ('is_company', '=', False)
        ], limit=1)
        
        if not customer:
            customer = self.env['res.partner'].create({
                'name': email.split('@')[0].title(),
                'email': email,
                'is_company': False,
                'customer_rank': 1,
            })
        
        return customer

    def _get_product_by_code(self, product_code):
        """Get product by internal reference/code"""
        product = self.env['product.product'].search([
            ('default_code', '=', product_code)
        ], limit=1)
        
        if not product:
            _logger.warning(f"Product not found with code: {product_code}")
        
        return product


class ExampleInventorySync(models.Model):
    """
    Example: Synchronizing inventory levels with external systems
    
    This example shows how to create consumers that handle inventory
    updates and publish inventory changes to external systems.
    """
    _name = 'example.inventory.sync'
    _description = 'Example Inventory Sync'

    @api.model
    def setup_inventory_consumers(self):
        """Setup consumers for inventory synchronization"""
        try:
            server = self.env['rabbitmq.server'].get_default_server()
            
            # Consumer for incoming inventory updates
            incoming_consumer = {
                'name': 'Inventory Updates Receiver',
                'server_id': server.id,
                'queue_name': 'inventory.updates',
                'exchange_name': 'inventory',
                'routing_key': 'inventory.update.*',
                'consumer_class': 'example.inventory.sync',
                'consumer_method': 'process_inventory_update',
                'auto_start': True,
                'max_retries': 5,
                'retry_delay': 60,
                'prefetch_count': 50,
                'active': True,
            }
            
            self.env['rabbitmq.consumer'].create(incoming_consumer)
            
            _logger.info("Inventory sync consumers setup completed")
            
        except Exception as e:
            _logger.error(f"Error setting up inventory consumers: {str(e)}")
            raise

    @api.model
    def process_inventory_update(self, message_body, message_properties=None):
        """
        Process inventory update messages
        
        Expected message format:
        {
            "product_code": "PROD001",
            "location_code": "WH/Stock",
            "quantity": 100,
            "operation": "set|add|subtract",
            "reason": "Physical Inventory",
            "timestamp": "2024-01-01T12:00:00Z"
        }
        """
        try:
            # Parse message
            if isinstance(message_body, str):
                inventory_data = json.loads(message_body)
            else:
                inventory_data = message_body
            
            # Find product
            product = self.env['product.product'].search([
                ('default_code', '=', inventory_data['product_code'])
            ], limit=1)
            
            if not product:
                raise ValidationError(_("Product not found: %s") % inventory_data['product_code'])
            
            # Find location
            location = self.env['stock.location'].search([
                ('complete_name', 'ilike', inventory_data['location_code'])
            ], limit=1)
            
            if not location:
                location = self.env['stock.location'].search([
                ('usage', '=', 'internal')
            ], limit=1)  # Use default internal location
            
            # Create inventory adjustment
            inventory_vals = {
                'name': f"RabbitMQ Sync - {inventory_data['product_code']}",
                'location_ids': [(4, location.id)],
                'product_ids': [(4, product.id)],
                'state': 'draft',
            }
            
            inventory = self.env['stock.inventory'].create(inventory_vals)
            inventory.action_start()
            
            # Update inventory line
            inventory_line = inventory.line_ids.filtered(
                lambda l: l.product_id == product and l.location_id == location
            )
            
            if inventory_line:
                operation = inventory_data.get('operation', 'set')
                new_quantity = inventory_data['quantity']
                
                if operation == 'add':
                    new_quantity = inventory_line.theoretical_qty + new_quantity
                elif operation == 'subtract':
                    new_quantity = inventory_line.theoretical_qty - new_quantity
                
                inventory_line.product_qty = new_quantity
            
            # Validate and apply
            inventory.action_validate()
            
            _logger.info(f"Updated inventory for {product.default_code}: {inventory_data['quantity']}")
            
            return {'status': 'success', 'inventory_id': inventory.id}
            
        except Exception as e:
            _logger.error(f"Error processing inventory update: {str(e)}")
            raise

    @api.model
    def publish_inventory_change(self, product_id, location_id, old_qty, new_qty):
        """Publish inventory changes to external systems"""
        try:
            product = self.env['product.product'].browse(product_id)
            location = self.env['stock.location'].browse(location_id)
            
            message_data = {
                'product_code': product.default_code,
                'product_name': product.name,
                'location_code': location.complete_name,
                'old_quantity': old_qty,
                'new_quantity': new_qty,
                'change': new_qty - old_qty,
                'timestamp': datetime.now().isoformat(),
                'source': 'odoo'
            }
            
            # Publish to RabbitMQ
            server = self.env['rabbitmq.server'].get_default_server()
            server.publish_message(
                exchange='inventory',
                routing_key='inventory.changed',
                message=json.dumps(message_data),
                properties={'content_type': 'application/json'}
            )
            
            _logger.info(f"Published inventory change for {product.default_code}")
            
        except Exception as e:
            _logger.error(f"Error publishing inventory change: {str(e)}")


class ExampleNotificationHandler(models.Model):
    """
    Example: Handling various notification types
    
    This example shows how to create a generic notification handler
    that can process different types of messages and trigger appropriate actions.
    """
    _name = 'example.notification.handler'
    _description = 'Example Notification Handler'

    @api.model
    def setup_notification_consumer(self):
        """Setup consumer for general notifications"""
        try:
            server = self.env['rabbitmq.server'].get_default_server()
            
            consumer_vals = {
                'name': 'General Notifications',
                'server_id': server.id,
                'queue_name': 'notifications.general',
                'exchange_name': 'notifications',
                'routing_key': 'notification.*',
                'consumer_class': 'example.notification.handler',
                'consumer_method': 'process_notification',
                'auto_start': True,
                'max_retries': 3,
                'retry_delay': 30,
                'prefetch_count': 20,
                'active': True,
            }
            
            self.env['rabbitmq.consumer'].create(consumer_vals)
            
        except Exception as e:
            _logger.error(f"Error setting up notification consumer: {str(e)}")
            raise

    @api.model
    def process_notification(self, message_body, message_properties=None):
        """
        Process notification messages
        
        Expected message format:
        {
            "type": "email|sms|push|webhook",
            "recipient": "<EMAIL>",
            "subject": "Notification Subject",
            "message": "Notification content",
            "data": {...},
            "priority": "low|normal|high|urgent",
            "timestamp": "2024-01-01T12:00:00Z"
        }
        """
        try:
            # Parse message
            if isinstance(message_body, str):
                notification_data = json.loads(message_body)
            else:
                notification_data = message_body
            
            notification_type = notification_data.get('type', 'email')
            
            if notification_type == 'email':
                return self._send_email_notification(notification_data)
            elif notification_type == 'sms':
                return self._send_sms_notification(notification_data)
            elif notification_type == 'push':
                return self._send_push_notification(notification_data)
            elif notification_type == 'webhook':
                return self._send_webhook_notification(notification_data)
            else:
                raise ValidationError(_("Unknown notification type: %s") % notification_type)
                
        except Exception as e:
            _logger.error(f"Error processing notification: {str(e)}")
            raise

    def _send_email_notification(self, notification_data):
        """Send email notification using Odoo's mail system"""
        try:
            # Create mail message
            mail_vals = {
                'subject': notification_data.get('subject', 'Notification'),
                'body_html': notification_data.get('message', ''),
                'email_to': notification_data.get('recipient'),
                'auto_delete': True,
            }
            
            mail = self.env['mail.mail'].create(mail_vals)
            mail.send()
            
            _logger.info(f"Sent email notification to {notification_data.get('recipient')}")
            
            return {'status': 'success', 'mail_id': mail.id}
            
        except Exception as e:
            _logger.error(f"Error sending email notification: {str(e)}")
            raise

    def _send_sms_notification(self, notification_data):
        """Send SMS notification (placeholder implementation)"""
        # This would integrate with your SMS provider
        _logger.info(f"SMS notification would be sent to {notification_data.get('recipient')}")
        return {'status': 'success', 'message': 'SMS sent (simulated)'}

    def _send_push_notification(self, notification_data):
        """Send push notification (placeholder implementation)"""
        # This would integrate with your push notification service
        _logger.info(f"Push notification would be sent to {notification_data.get('recipient')}")
        return {'status': 'success', 'message': 'Push notification sent (simulated)'}

    def _send_webhook_notification(self, notification_data):
        """Send webhook notification"""
        try:
            import requests
            
            webhook_url = notification_data.get('webhook_url')
            if not webhook_url:
                raise ValidationError(_("Webhook URL is required"))
            
            payload = {
                'subject': notification_data.get('subject'),
                'message': notification_data.get('message'),
                'data': notification_data.get('data', {}),
                'timestamp': notification_data.get('timestamp'),
            }
            
            response = requests.post(webhook_url, json=payload, timeout=30)
            response.raise_for_status()
            
            _logger.info(f"Sent webhook notification to {webhook_url}")
            
            return {'status': 'success', 'response_code': response.status_code}
            
        except Exception as e:
            _logger.error(f"Error sending webhook notification: {str(e)}")
            raise
