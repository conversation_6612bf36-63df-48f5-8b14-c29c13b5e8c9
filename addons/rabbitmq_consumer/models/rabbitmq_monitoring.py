# -*- coding: utf-8 -*-

import json
import logging
from datetime import datetime, timedelta

from odoo import api, fields, models, _
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class RabbitMQMonitoring(models.Model):
    _name = 'rabbitmq.monitoring'
    _description = 'RabbitMQ Monitoring and Health Checks'
    _order = 'create_date desc'

    name = fields.Char('Check Name', required=True)
    check_type = fields.Selection([
        ('health', 'Health Check'),
        ('performance', 'Performance Check'),
        ('connectivity', 'Connectivity Check'),
        ('consumer', 'Consumer Check'),
        ('message', 'Message Check'),
    ], string='Check Type', required=True)
    
    status = fields.Selection([
        ('healthy', 'Healthy'),
        ('warning', 'Warning'),
        ('critical', 'Critical'),
        ('unknown', 'Unknown'),
    ], string='Status', required=True, default='unknown')
    
    # Check Details
    server_id = fields.Many2one('rabbitmq.server', string='Server')
    consumer_id = fields.Many2one('rabbitmq.consumer', string='Consumer')
    container_id = fields.Char('Container ID')
    
    # Results
    check_result = fields.Text('Check Result', help="JSON formatted check results")
    error_message = fields.Text('Error Message')
    response_time = fields.Float('Response Time (ms)')
    
    # Metrics
    metric_value = fields.Float('Metric Value')
    metric_unit = fields.Char('Metric Unit')
    threshold_warning = fields.Float('Warning Threshold')
    threshold_critical = fields.Float('Critical Threshold')
    
    # Timestamps
    check_time = fields.Datetime('Check Time', default=fields.Datetime.now)
    next_check = fields.Datetime('Next Check')
    
    @api.model
    def run_health_checks(self):
        """Run all health checks"""
        results = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'healthy',
            'checks': {}
        }
        
        try:
            # Check RabbitMQ servers
            server_results = self._check_servers()
            results['checks']['servers'] = server_results
            
            # Check consumers
            consumer_results = self._check_consumers()
            results['checks']['consumers'] = consumer_results
            
            # Check message processing
            message_results = self._check_message_processing()
            results['checks']['messages'] = message_results
            
            # Check container health
            container_results = self._check_containers()
            results['checks']['containers'] = container_results
            
            # Determine overall status
            all_checks = [server_results, consumer_results, message_results, container_results]
            if any(check.get('status') == 'critical' for check in all_checks):
                results['overall_status'] = 'critical'
            elif any(check.get('status') == 'warning' for check in all_checks):
                results['overall_status'] = 'warning'
            
            # Log results
            self.create({
                'name': 'System Health Check',
                'check_type': 'health',
                'status': results['overall_status'],
                'check_result': json.dumps(results),
            })
            
            return results
            
        except Exception as e:
            _logger.error(f"Error running health checks: {str(e)}")
            error_result = {
                'timestamp': datetime.now().isoformat(),
                'overall_status': 'critical',
                'error': str(e)
            }
            
            self.create({
                'name': 'System Health Check',
                'check_type': 'health',
                'status': 'critical',
                'error_message': str(e),
                'check_result': json.dumps(error_result),
            })
            
            return error_result

    def _check_servers(self):
        """Check RabbitMQ server connectivity"""
        try:
            servers = self.env['rabbitmq.server'].search([('active', '=', True)])
            server_status = {
                'status': 'healthy',
                'total_servers': len(servers),
                'connected': 0,
                'failed': 0,
                'details': []
            }
            
            for server in servers:
                start_time = datetime.now()
                try:
                    server.test_connection()
                    response_time = (datetime.now() - start_time).total_seconds() * 1000
                    
                    server_status['connected'] += 1
                    server_status['details'].append({
                        'server': server.name,
                        'status': 'connected',
                        'response_time_ms': response_time
                    })
                    
                    # Log individual server check
                    self.create({
                        'name': f'Server Connectivity: {server.name}',
                        'check_type': 'connectivity',
                        'status': 'healthy',
                        'server_id': server.id,
                        'response_time': response_time,
                    })
                    
                except Exception as e:
                    server_status['failed'] += 1
                    server_status['details'].append({
                        'server': server.name,
                        'status': 'failed',
                        'error': str(e)
                    })
                    
                    # Log failed server check
                    self.create({
                        'name': f'Server Connectivity: {server.name}',
                        'check_type': 'connectivity',
                        'status': 'critical',
                        'server_id': server.id,
                        'error_message': str(e),
                    })
            
            # Determine overall server status
            if server_status['failed'] > 0:
                if server_status['connected'] == 0:
                    server_status['status'] = 'critical'
                else:
                    server_status['status'] = 'warning'
            
            return server_status
            
        except Exception as e:
            _logger.error(f"Error checking servers: {str(e)}")
            return {'status': 'critical', 'error': str(e)}

    def _check_consumers(self):
        """Check consumer health"""
        try:
            consumers = self.env['rabbitmq.consumer'].search([('active', '=', True)])
            consumer_status = {
                'status': 'healthy',
                'total_consumers': len(consumers),
                'running': 0,
                'stopped': 0,
                'failed': 0,
                'details': []
            }
            
            for consumer in consumers:
                if consumer.status == 'running':
                    consumer_status['running'] += 1
                    status = 'healthy'
                elif consumer.status == 'failed':
                    consumer_status['failed'] += 1
                    status = 'critical'
                else:
                    consumer_status['stopped'] += 1
                    status = 'warning' if consumer.auto_start else 'healthy'
                
                consumer_status['details'].append({
                    'consumer': consumer.name,
                    'status': consumer.status,
                    'queue': consumer.queue_name,
                    'messages_processed': consumer.messages_processed,
                    'messages_failed': consumer.messages_failed,
                    'error_count': consumer.error_count,
                    'container_id': consumer.container_id,
                })
                
                # Log individual consumer check
                self.create({
                    'name': f'Consumer Health: {consumer.name}',
                    'check_type': 'consumer',
                    'status': status,
                    'consumer_id': consumer.id,
                    'metric_value': consumer.error_count,
                    'metric_unit': 'errors',
                    'error_message': consumer.last_error if consumer.last_error else None,
                })
            
            # Determine overall consumer status
            if consumer_status['failed'] > 0:
                consumer_status['status'] = 'critical'
            elif consumer_status['stopped'] > consumer_status['running']:
                consumer_status['status'] = 'warning'
            
            return consumer_status
            
        except Exception as e:
            _logger.error(f"Error checking consumers: {str(e)}")
            return {'status': 'critical', 'error': str(e)}

    def _check_message_processing(self):
        """Check message processing performance"""
        try:
            # Get message statistics for the last hour
            one_hour_ago = datetime.now() - timedelta(hours=1)
            
            messages = self.env['rabbitmq.message'].search([
                ('create_date', '>=', one_hour_ago)
            ])
            
            message_status = {
                'status': 'healthy',
                'total_messages': len(messages),
                'processed': 0,
                'failed': 0,
                'pending': 0,
                'avg_processing_time': 0,
                'error_rate': 0,
            }
            
            if messages:
                processed_messages = messages.filtered(lambda m: m.status == 'processed')
                failed_messages = messages.filtered(lambda m: m.status == 'failed')
                pending_messages = messages.filtered(lambda m: m.status in ('pending', 'processing'))
                
                message_status['processed'] = len(processed_messages)
                message_status['failed'] = len(failed_messages)
                message_status['pending'] = len(pending_messages)
                
                # Calculate average processing time
                if processed_messages:
                    avg_time = sum(processed_messages.mapped('processing_duration')) / len(processed_messages)
                    message_status['avg_processing_time'] = avg_time
                
                # Calculate error rate
                message_status['error_rate'] = len(failed_messages) / len(messages) * 100
                
                # Determine status based on error rate and processing time
                if message_status['error_rate'] > 10:  # More than 10% errors
                    message_status['status'] = 'critical'
                elif message_status['error_rate'] > 5 or message_status['avg_processing_time'] > 30:
                    message_status['status'] = 'warning'
            
            # Log message processing check
            self.create({
                'name': 'Message Processing Performance',
                'check_type': 'performance',
                'status': message_status['status'],
                'metric_value': message_status['error_rate'],
                'metric_unit': 'error_rate_%',
                'threshold_warning': 5.0,
                'threshold_critical': 10.0,
                'check_result': json.dumps(message_status),
            })
            
            return message_status
            
        except Exception as e:
            _logger.error(f"Error checking message processing: {str(e)}")
            return {'status': 'critical', 'error': str(e)}

    def _check_containers(self):
        """Check container health"""
        try:
            managers = self.env['rabbitmq.consumer.manager'].search([])
            container_status = {
                'status': 'healthy',
                'total_containers': len(managers),
                'running': 0,
                'stale': 0,
                'details': []
            }
            
            stale_threshold = datetime.now() - timedelta(minutes=5)
            
            for manager in managers:
                is_stale = manager.last_heartbeat < stale_threshold if manager.last_heartbeat else True
                
                if is_stale:
                    container_status['stale'] += 1
                    status = 'critical'
                else:
                    container_status['running'] += 1
                    status = 'healthy'
                
                container_status['details'].append({
                    'container_id': manager.container_id,
                    'status': manager.status,
                    'consumer_count': manager.consumer_count,
                    'last_heartbeat': manager.last_heartbeat.isoformat() if manager.last_heartbeat else None,
                    'is_stale': is_stale,
                })
                
                # Log individual container check
                self.create({
                    'name': f'Container Health: {manager.container_id}',
                    'check_type': 'health',
                    'status': status,
                    'container_id': manager.container_id,
                    'metric_value': manager.consumer_count,
                    'metric_unit': 'consumers',
                })
            
            # Determine overall container status
            if container_status['stale'] > 0:
                container_status['status'] = 'warning'
            
            return container_status
            
        except Exception as e:
            _logger.error(f"Error checking containers: {str(e)}")
            return {'status': 'critical', 'error': str(e)}

    @api.model
    def get_dashboard_data(self):
        """Get monitoring dashboard data"""
        try:
            # Get latest health check
            latest_check = self.search([
                ('check_type', '=', 'health'),
                ('name', '=', 'System Health Check')
            ], limit=1, order='create_date desc')
            
            dashboard_data = {
                'last_check': latest_check.check_time.isoformat() if latest_check else None,
                'overall_status': latest_check.status if latest_check else 'unknown',
                'servers': self._get_server_summary(),
                'consumers': self._get_consumer_summary(),
                'messages': self._get_message_summary(),
                'alerts': self._get_recent_alerts(),
            }
            
            return dashboard_data
            
        except Exception as e:
            _logger.error(f"Error getting dashboard data: {str(e)}")
            return {'error': str(e)}

    def _get_server_summary(self):
        """Get server summary for dashboard"""
        servers = self.env['rabbitmq.server'].search([('active', '=', True)])
        connected = servers.filtered(lambda s: s.connection_status == 'connected')
        
        return {
            'total': len(servers),
            'connected': len(connected),
            'failed': len(servers) - len(connected),
        }

    def _get_consumer_summary(self):
        """Get consumer summary for dashboard"""
        consumers = self.env['rabbitmq.consumer'].search([('active', '=', True)])
        
        return {
            'total': len(consumers),
            'running': len(consumers.filtered(lambda c: c.status == 'running')),
            'stopped': len(consumers.filtered(lambda c: c.status == 'stopped')),
            'failed': len(consumers.filtered(lambda c: c.status == 'failed')),
        }

    def _get_message_summary(self):
        """Get message summary for dashboard"""
        today = datetime.now().strftime('%Y-%m-%d')
        messages_today = self.env['rabbitmq.message'].search([
            ('create_date', '>=', today)
        ])
        
        return {
            'today_total': len(messages_today),
            'today_processed': len(messages_today.filtered(lambda m: m.status == 'processed')),
            'today_failed': len(messages_today.filtered(lambda m: m.status == 'failed')),
            'today_pending': len(messages_today.filtered(lambda m: m.status in ('pending', 'processing'))),
        }

    def _get_recent_alerts(self):
        """Get recent alerts for dashboard"""
        recent_alerts = self.search([
            ('status', 'in', ['warning', 'critical']),
            ('create_date', '>=', datetime.now() - timedelta(hours=24))
        ], limit=10, order='create_date desc')
        
        return [{
            'time': alert.check_time.isoformat(),
            'type': alert.check_type,
            'status': alert.status,
            'message': alert.name,
            'error': alert.error_message,
        } for alert in recent_alerts]

    @api.model
    def cleanup_old_monitoring_data(self, days=7):
        """Clean up old monitoring data"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        old_records = self.search([
            ('create_date', '<', cutoff_date)
        ])
        
        count = len(old_records)
        old_records.unlink()
        
        _logger.info(f"Cleaned up {count} old monitoring records older than {days} days")
        return count
