# -*- coding: utf-8 -*-

import json
import logging
import os
import traceback
from datetime import datetime
from functools import wraps

from odoo import api, models, _

_logger = logging.getLogger(__name__)


class RabbitMQLoggingService(models.TransientModel):
    _name = 'rabbitmq.logging.service'
    _description = 'RabbitMQ Logging Service'

    @api.model
    def setup_logging(self):
        """Setup enhanced logging for RabbitMQ operations"""
        try:
            # Create RabbitMQ specific logger
            rabbitmq_logger = logging.getLogger('rabbitmq_consumer')
            
            # Set log level based on configuration
            params = self.env['ir.config_parameter'].sudo()
            log_level = params.get_param('rabbitmq_consumer.log_level', 'INFO')
            rabbitmq_logger.setLevel(getattr(logging, log_level.upper()))
            
            # Create formatter
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - [%(container_id)s] - %(message)s'
            )
            
            # Add container ID to log records
            container_id = os.environ.get('HOSTNAME', 'unknown')
            
            class ContainerFilter(logging.Filter):
                def filter(self, record):
                    record.container_id = container_id
                    return True
            
            rabbitmq_logger.addFilter(ContainerFilter())
            
            # Setup file handler if configured
            log_file = params.get_param('rabbitmq_consumer.log_file')
            if log_file:
                file_handler = logging.FileHandler(log_file)
                file_handler.setFormatter(formatter)
                rabbitmq_logger.addHandler(file_handler)
            
            _logger.info("RabbitMQ logging setup completed")
            return True
            
        except Exception as e:
            _logger.error(f"Error setting up logging: {str(e)}")
            return False

    @api.model
    def log_consumer_event(self, consumer_id, event_type, message, level='info', extra_data=None):
        """Log consumer-specific events"""
        try:
            # Get consumer info
            consumer = self.env['rabbitmq.consumer'].browse(consumer_id) if consumer_id else None
            
            log_data = {
                'timestamp': datetime.now().isoformat(),
                'event_type': event_type,
                'consumer_id': consumer_id,
                'consumer_name': consumer.name if consumer else 'Unknown',
                'queue_name': consumer.queue_name if consumer else 'Unknown',
                'container_id': os.environ.get('HOSTNAME', 'unknown'),
                'message': message,
                'extra_data': extra_data or {}
            }
            
            # Log to standard logger
            logger = logging.getLogger('rabbitmq_consumer.consumer')
            log_method = getattr(logger, level.lower(), logger.info)
            log_method(f"[{event_type}] {message} - {json.dumps(log_data)}")
            
            # Store in database if configured
            params = self.env['ir.config_parameter'].sudo()
            store_logs = params.get_param('rabbitmq_consumer.store_logs_in_db', 'True') == 'True'
            
            if store_logs:
                self.env['rabbitmq.monitoring'].create({
                    'name': f"Consumer Event: {event_type}",
                    'check_type': 'consumer',
                    'status': 'healthy' if level in ('info', 'debug') else 'warning' if level == 'warning' else 'critical',
                    'consumer_id': consumer_id,
                    'container_id': os.environ.get('HOSTNAME', 'unknown'),
                    'check_result': json.dumps(log_data),
                    'error_message': message if level in ('error', 'critical') else None,
                })
            
            return True
            
        except Exception as e:
            _logger.error(f"Error logging consumer event: {str(e)}")
            return False

    @api.model
    def log_message_event(self, message_id, event_type, message, level='info', extra_data=None):
        """Log message processing events"""
        try:
            # Get message info
            msg_record = self.env['rabbitmq.message'].browse(message_id) if message_id else None
            
            log_data = {
                'timestamp': datetime.now().isoformat(),
                'event_type': event_type,
                'message_id': message_id,
                'queue_name': msg_record.queue_name if msg_record else 'Unknown',
                'message_type': msg_record.message_type if msg_record else 'Unknown',
                'container_id': os.environ.get('HOSTNAME', 'unknown'),
                'message': message,
                'extra_data': extra_data or {}
            }
            
            # Log to standard logger
            logger = logging.getLogger('rabbitmq_consumer.message')
            log_method = getattr(logger, level.lower(), logger.info)
            log_method(f"[{event_type}] {message} - {json.dumps(log_data)}")
            
            return True
            
        except Exception as e:
            _logger.error(f"Error logging message event: {str(e)}")
            return False

    @api.model
    def log_connection_event(self, server_id, event_type, message, level='info', extra_data=None):
        """Log connection events"""
        try:
            # Get server info
            server = self.env['rabbitmq.server'].browse(server_id) if server_id else None
            
            log_data = {
                'timestamp': datetime.now().isoformat(),
                'event_type': event_type,
                'server_id': server_id,
                'server_name': server.name if server else 'Unknown',
                'server_host': server.host if server else 'Unknown',
                'container_id': os.environ.get('HOSTNAME', 'unknown'),
                'message': message,
                'extra_data': extra_data or {}
            }
            
            # Log to standard logger
            logger = logging.getLogger('rabbitmq_consumer.connection')
            log_method = getattr(logger, level.lower(), logger.info)
            log_method(f"[{event_type}] {message} - {json.dumps(log_data)}")
            
            # Store critical connection events in monitoring
            if level in ('error', 'critical'):
                self.env['rabbitmq.monitoring'].create({
                    'name': f"Connection Event: {event_type}",
                    'check_type': 'connectivity',
                    'status': 'critical',
                    'server_id': server_id,
                    'container_id': os.environ.get('HOSTNAME', 'unknown'),
                    'check_result': json.dumps(log_data),
                    'error_message': message,
                })
            
            return True
            
        except Exception as e:
            _logger.error(f"Error logging connection event: {str(e)}")
            return False

    @api.model
    def log_performance_metric(self, metric_name, metric_value, metric_unit, context_data=None):
        """Log performance metrics"""
        try:
            log_data = {
                'timestamp': datetime.now().isoformat(),
                'metric_name': metric_name,
                'metric_value': metric_value,
                'metric_unit': metric_unit,
                'container_id': os.environ.get('HOSTNAME', 'unknown'),
                'context_data': context_data or {}
            }
            
            # Log to standard logger
            logger = logging.getLogger('rabbitmq_consumer.performance')
            logger.info(f"[METRIC] {metric_name}: {metric_value} {metric_unit} - {json.dumps(log_data)}")
            
            # Store performance metrics in monitoring
            params = self.env['ir.config_parameter'].sudo()
            store_metrics = params.get_param('rabbitmq_consumer.store_metrics_in_db', 'True') == 'True'
            
            if store_metrics:
                # Determine status based on thresholds (if configured)
                status = 'healthy'
                warning_threshold = context_data.get('warning_threshold') if context_data else None
                critical_threshold = context_data.get('critical_threshold') if context_data else None
                
                if critical_threshold and metric_value >= critical_threshold:
                    status = 'critical'
                elif warning_threshold and metric_value >= warning_threshold:
                    status = 'warning'
                
                self.env['rabbitmq.monitoring'].create({
                    'name': f"Performance Metric: {metric_name}",
                    'check_type': 'performance',
                    'status': status,
                    'container_id': os.environ.get('HOSTNAME', 'unknown'),
                    'metric_value': metric_value,
                    'metric_unit': metric_unit,
                    'threshold_warning': warning_threshold,
                    'threshold_critical': critical_threshold,
                    'check_result': json.dumps(log_data),
                })
            
            return True
            
        except Exception as e:
            _logger.error(f"Error logging performance metric: {str(e)}")
            return False

    @api.model
    def log_error_with_traceback(self, error, context=None, level='error'):
        """Log errors with full traceback"""
        try:
            error_data = {
                'timestamp': datetime.now().isoformat(),
                'error_type': type(error).__name__,
                'error_message': str(error),
                'traceback': traceback.format_exc(),
                'container_id': os.environ.get('HOSTNAME', 'unknown'),
                'context': context or {}
            }
            
            # Log to standard logger
            logger = logging.getLogger('rabbitmq_consumer.error')
            log_method = getattr(logger, level.lower(), logger.error)
            log_method(f"[ERROR] {str(error)} - {json.dumps(error_data)}")
            
            # Store critical errors in monitoring
            if level in ('error', 'critical'):
                self.env['rabbitmq.monitoring'].create({
                    'name': f"System Error: {type(error).__name__}",
                    'check_type': 'health',
                    'status': 'critical',
                    'container_id': os.environ.get('HOSTNAME', 'unknown'),
                    'error_message': str(error),
                    'check_result': json.dumps(error_data),
                })
            
            return True
            
        except Exception as e:
            _logger.error(f"Error logging error with traceback: {str(e)}")
            return False

    @api.model
    def get_log_statistics(self, hours=24):
        """Get logging statistics for the specified time period"""
        try:
            from datetime import timedelta
            
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            # Get monitoring records
            monitoring_records = self.env['rabbitmq.monitoring'].search([
                ('create_date', '>=', cutoff_time)
            ])
            
            stats = {
                'total_events': len(monitoring_records),
                'by_status': {},
                'by_type': {},
                'by_container': {},
                'error_rate': 0,
                'critical_events': 0,
            }
            
            for record in monitoring_records:
                # Count by status
                status = record.status
                stats['by_status'][status] = stats['by_status'].get(status, 0) + 1
                
                # Count by type
                check_type = record.check_type
                stats['by_type'][check_type] = stats['by_type'].get(check_type, 0) + 1
                
                # Count by container
                container = record.container_id or 'unknown'
                stats['by_container'][container] = stats['by_container'].get(container, 0) + 1
                
                # Count critical events
                if status == 'critical':
                    stats['critical_events'] += 1
            
            # Calculate error rate
            if stats['total_events'] > 0:
                error_events = stats['by_status'].get('warning', 0) + stats['by_status'].get('critical', 0)
                stats['error_rate'] = (error_events / stats['total_events']) * 100
            
            return stats
            
        except Exception as e:
            _logger.error(f"Error getting log statistics: {str(e)}")
            return {'error': str(e)}


def log_rabbitmq_operation(operation_name, log_level='info'):
    """Decorator to automatically log RabbitMQ operations"""
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            start_time = datetime.now()
            
            try:
                # Log operation start
                logging_service = self.env['rabbitmq.logging.service']
                logging_service.log_performance_metric(
                    f"{operation_name}_started",
                    1,
                    'count',
                    {'function': func.__name__, 'args_count': len(args)}
                )
                
                # Execute function
                result = func(self, *args, **kwargs)
                
                # Log operation success
                duration = (datetime.now() - start_time).total_seconds() * 1000
                logging_service.log_performance_metric(
                    f"{operation_name}_duration",
                    duration,
                    'ms',
                    {'function': func.__name__, 'success': True}
                )
                
                return result
                
            except Exception as e:
                # Log operation failure
                duration = (datetime.now() - start_time).total_seconds() * 1000
                logging_service = self.env['rabbitmq.logging.service']
                logging_service.log_error_with_traceback(
                    e,
                    {
                        'operation': operation_name,
                        'function': func.__name__,
                        'duration_ms': duration,
                        'args_count': len(args)
                    },
                    'error'
                )
                raise
                
        return wrapper
    return decorator
