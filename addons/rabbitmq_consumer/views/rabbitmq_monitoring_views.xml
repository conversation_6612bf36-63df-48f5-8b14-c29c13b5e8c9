<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- RabbitMQ Monitoring Tree View -->
        <record id="rabbitmq_monitoring_view_tree" model="ir.ui.view">
            <field name="name">rabbitmq.monitoring.tree</field>
            <field name="model">rabbitmq.monitoring</field>
            <field name="arch" type="xml">
                <tree string="RabbitMQ Monitoring" decoration-success="status=='healthy'" decoration-warning="status=='warning'" decoration-danger="status=='critical'">
                    <field name="check_time"/>
                    <field name="name"/>
                    <field name="check_type"/>
                    <field name="status"/>
                    <field name="server_id"/>
                    <field name="consumer_id"/>
                    <field name="container_id"/>
                    <field name="response_time"/>
                    <field name="metric_value"/>
                    <field name="metric_unit"/>
                    <field name="error_message"/>
                </tree>
            </field>
        </record>
        
        <!-- RabbitMQ Monitoring Form View -->
        <record id="rabbitmq_monitoring_view_form" model="ir.ui.view">
            <field name="name">rabbitmq.monitoring.form</field>
            <field name="model">rabbitmq.monitoring</field>
            <field name="arch" type="xml">
                <form string="RabbitMQ Monitoring" create="false" edit="false">
                    <header>
                        <field name="status" widget="statusbar" statusbar_visible="healthy,warning,critical,unknown"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <label for="name" class="oe_edit_only"/>
                            <h1><field name="name"/></h1>
                        </div>
                        
                        <group>
                            <group name="check_info" string="Check Information">
                                <field name="check_type"/>
                                <field name="check_time"/>
                                <field name="next_check"/>
                                <field name="response_time"/>
                            </group>
                            
                            <group name="target_info" string="Target Information">
                                <field name="server_id"/>
                                <field name="consumer_id"/>
                                <field name="container_id"/>
                            </group>
                        </group>
                        
                        <group name="metrics" string="Metrics" attrs="{'invisible': [('metric_value', '=', False)]}">
                            <field name="metric_value"/>
                            <field name="metric_unit"/>
                            <field name="threshold_warning"/>
                            <field name="threshold_critical"/>
                        </group>
                        
                        <group name="error_info" string="Error Information" attrs="{'invisible': [('error_message', '=', False)]}">
                            <field name="error_message" widget="text"/>
                        </group>
                        
                        <notebook>
                            <page string="Check Results" name="check_results" attrs="{'invisible': [('check_result', '=', False)]}">
                                <field name="check_result" widget="ace" options="{'mode': 'json'}"/>
                            </page>
                        </notebook>
                        
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- RabbitMQ Monitoring Search View -->
        <record id="rabbitmq_monitoring_view_search" model="ir.ui.view">
            <field name="name">rabbitmq.monitoring.search</field>
            <field name="model">rabbitmq.monitoring</field>
            <field name="arch" type="xml">
                <search string="RabbitMQ Monitoring">
                    <field name="name"/>
                    <field name="check_type"/>
                    <field name="server_id"/>
                    <field name="consumer_id"/>
                    <field name="container_id"/>
                    <filter string="Healthy" name="healthy" domain="[('status', '=', 'healthy')]"/>
                    <filter string="Warning" name="warning" domain="[('status', '=', 'warning')]"/>
                    <filter string="Critical" name="critical" domain="[('status', '=', 'critical')]"/>
                    <separator/>
                    <filter string="Health Checks" name="health_checks" domain="[('check_type', '=', 'health')]"/>
                    <filter string="Performance Checks" name="performance_checks" domain="[('check_type', '=', 'performance')]"/>
                    <filter string="Connectivity Checks" name="connectivity_checks" domain="[('check_type', '=', 'connectivity')]"/>
                    <filter string="Consumer Checks" name="consumer_checks" domain="[('check_type', '=', 'consumer')]"/>
                    <separator/>
                    <filter string="Today" name="today" domain="[('check_time', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0)))]"/>
                    <filter string="Last 24 Hours" name="last_24h" domain="[('check_time', '>=', (datetime.datetime.now() - datetime.timedelta(hours=24)).strftime('%Y-%m-%d %H:%M:%S'))]"/>
                    <filter string="Last 7 Days" name="last_week" domain="[('check_time', '>=', (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                    <group expand="0" string="Group By">
                        <filter string="Status" name="group_status" context="{'group_by': 'status'}"/>
                        <filter string="Check Type" name="group_type" context="{'group_by': 'check_type'}"/>
                        <filter string="Server" name="group_server" context="{'group_by': 'server_id'}"/>
                        <filter string="Consumer" name="group_consumer" context="{'group_by': 'consumer_id'}"/>
                        <filter string="Container" name="group_container" context="{'group_by': 'container_id'}"/>
                        <filter string="Check Date" name="group_date" context="{'group_by': 'check_time:day'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- RabbitMQ Monitoring Dashboard -->
        <record id="rabbitmq_monitoring_dashboard" model="ir.ui.view">
            <field name="name">rabbitmq.monitoring.dashboard</field>
            <field name="model">rabbitmq.monitoring</field>
            <field name="arch" type="xml">
                <dashboard string="RabbitMQ Monitoring Dashboard">
                    <view type="graph" ref="rabbitmq_monitoring_view_graph"/>
                    <group expand="1" string="System Status">
                        <aggregate name="total_checks" string="Total Checks" field="id" widget="integer"/>
                        <aggregate name="healthy_checks" string="Healthy" field="id" domain="[('status', '=', 'healthy')]" widget="integer"/>
                        <aggregate name="warning_checks" string="Warnings" field="id" domain="[('status', '=', 'warning')]" widget="integer"/>
                        <aggregate name="critical_checks" string="Critical" field="id" domain="[('status', '=', 'critical')]" widget="integer"/>
                    </group>
                    <group expand="1" string="Performance Metrics">
                        <aggregate name="avg_response_time" string="Avg Response Time" field="response_time" widget="float"/>
                        <aggregate name="max_response_time" string="Max Response Time" field="response_time" widget="float"/>
                    </group>
                </dashboard>
            </field>
        </record>
        
        <!-- RabbitMQ Monitoring Graph View -->
        <record id="rabbitmq_monitoring_view_graph" model="ir.ui.view">
            <field name="name">rabbitmq.monitoring.graph</field>
            <field name="model">rabbitmq.monitoring</field>
            <field name="arch" type="xml">
                <graph string="Monitoring Trends" type="line">
                    <field name="check_time" interval="hour"/>
                    <field name="status" type="row"/>
                    <field name="id" type="measure"/>
                </graph>
            </field>
        </record>
        
        <!-- RabbitMQ Monitoring Pivot View -->
        <record id="rabbitmq_monitoring_view_pivot" model="ir.ui.view">
            <field name="name">rabbitmq.monitoring.pivot</field>
            <field name="model">rabbitmq.monitoring</field>
            <field name="arch" type="xml">
                <pivot string="Monitoring Analysis">
                    <field name="check_type" type="row"/>
                    <field name="status" type="col"/>
                    <field name="id" type="measure"/>
                    <field name="response_time" type="measure"/>
                </pivot>
            </field>
        </record>
        
        <!-- RabbitMQ Monitoring Action -->
        <record id="rabbitmq_monitoring_action" model="ir.actions.act_window">
            <field name="name">RabbitMQ Monitoring</field>
            <field name="res_model">rabbitmq.monitoring</field>
            <field name="view_mode">tree,form,graph,pivot</field>
            <field name="context">{'search_default_today': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No monitoring data found
                </p>
                <p>
                    Monitoring data will appear here as health checks are performed.
                    Health checks run automatically via cron jobs.
                </p>
            </field>
        </record>
        
        <!-- RabbitMQ Dashboard Action -->
        <record id="rabbitmq_dashboard_action" model="ir.actions.act_window">
            <field name="name">RabbitMQ Dashboard</field>
            <field name="res_model">rabbitmq.monitoring</field>
            <field name="view_mode">dashboard,graph,pivot</field>
            <field name="view_id" ref="rabbitmq_monitoring_dashboard"/>
            <field name="context">{'search_default_last_24h': 1}</field>
        </record>
        
    </data>
</odoo>
